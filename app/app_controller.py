"""
Application Controller
======================

Main application controller coordinating between views and services.
"""

import flet as ft
from typing import Dict, Any, Optional, Callable
import logging
import asyncio
import time
from pathlib import Path
from datetime import datetime

from app.app_state import AppState
from models.ui_state import UIState, TabState
from models.client_profile import ClientProfile
from models.project_assumptions import EnhancedProjectAssumptions

from services.financial_service import FinancialModelService
from services.validation_service import ValidationService
from services.export_service import ExportService
from services.location_service import LocationComparisonService
from services.report_service import ReportGenerationService
from services.health_monitor import setup_health_monitoring, get_health_status
from services.error_handler import global_error_handler

# Enhanced Integration Service
from services.enhanced_integration_service import get_integration_service

from views.project_setup_view import ProjectSetupView
from views.dashboard_view import DashboardView
from views.location_comparison_view import LocationComparisonView
from views.financial_model_view import FinancialModelView
from views.validation_view import ValidationView
from views.sensitivity_view import SensitivityView
from views.monte_carlo_view import Monte<PERSON>ar<PERSON>View
from views.scenarios_view import Sc<PERSON><PERSON>sView
from views.export_view import ExportView

from utils.file_utils import FileUtils

from components.ui.loading_overlay import LoadingOverlay
from components.ui.notification_system import NotificationSystem
from components.ui.theme_manager import ThemeManager
from components.ui.enhanced_progress_overlay import EnhancedProgressOverlay
from components.widgets.status_bar import StatusBar

# Modern UI System
from components.ui.modern_theme_system import get_theme, set_theme_mode
from components.ui.modern_navigation import ModernSidebar, ModernBreadcrumb, ModernTopBar, create_navigation_items, NavigationState
from components.ui.modern_components import ModernButton, ModernCard, ButtonVariant, ComponentSize


class AppController:
    """Main application controller."""
    
    def __init__(self, page: ft.Page):
        self.page = page
        self.logger = logging.getLogger(__name__)
        
        # Initialize state
        self.app_state = AppState()
        self.ui_state = UIState()
        
        # Initialize services with error handling
        try:
            # Enhanced Integration Service (primary)
            self.enhanced_service = get_integration_service()
            
            # Standard services (for compatibility)
            self.financial_service = FinancialModelService()
            self.validation_service = ValidationService()
            self.export_service = ExportService()
            self.location_service = LocationComparisonService()
            self.report_service = ReportGenerationService()
            self.file_utils = FileUtils()

            # Setup health monitoring
            setup_health_monitoring()
            self.logger.info("All services initialized successfully with enhanced features and health monitoring")

        except Exception as e:
            global_error_handler.handle_error(e,
                context={'function': 'service_initialization'},
                show_user_message=True,
                page=self.page)
            self.logger.error(f"Failed to initialize services: {str(e)}")
        
        # Initialize views
        self.views = {}
        self._initialize_views()
        
        # UI components
        self.status_bar: Optional[ft.Container] = None
        self.progress_bar: Optional[ft.ProgressBar] = None
        self.main_content: Optional[ft.Container] = None
        self.navigation_rail: Optional[ft.NavigationRail] = None
        self.loading_overlay = LoadingOverlay(self.page)
        self.enhanced_progress_overlay = EnhancedProgressOverlay(self.page)
        self.notification_system = NotificationSystem(self.page)
        self.theme_manager = ThemeManager()
        
        # Modern UI System
        self.modern_theme = get_theme()
        self.modern_sidebar: Optional[ModernSidebar] = None
        self.modern_breadcrumb: Optional[ModernBreadcrumb] = None
        self.modern_topbar: Optional[ModernTopBar] = None
        self.current_breadcrumb: List[Dict[str, str]] = []
        
        # Apply modern theme to page
        self.modern_theme.apply_to_page(self.page)
        
        # Setup page
        self._setup_page()
    
    def _initialize_views(self):
        """Initialize all view components."""
        self.views = {
            TabState.PROJECT_SETUP: ProjectSetupView(self.page),
            TabState.DASHBOARD: DashboardView(self.page),
            TabState.LOCATION_COMPARISON: LocationComparisonView(self.page),
            TabState.FINANCIAL_MODEL: FinancialModelView(self.page),
            TabState.VALIDATION: ValidationView(self.page),
            TabState.SENSITIVITY: SensitivityView(self.page),
            TabState.MONTE_CARLO: MonteCarloView(self.page),
            TabState.SCENARIOS: ScenariosView(self.page),
            TabState.EXPORT: ExportView(self.page)
        }
        
        # Setup view callbacks
        for view in self.views.values():
            view.on_navigate = self.navigate_to_tab
            view.on_data_changed = self.handle_data_change
            view.on_action_requested = self.handle_action_request
            view.on_status_update = self.update_status
    
    def _setup_page(self):
        """Setup the main page layout."""
        self.page.title = "Hiel RnE Modeler v3.0 - Agevolami SRL"
        self.page.theme_mode = ft.ThemeMode.LIGHT
        self.page.padding = 0

        # Set window icon
        try:
            import os
            # Try .ico first (preferred for Windows), then .png as fallback
            if os.path.exists("assets/logo.ico"):
                self.page.window_icon = "assets/logo.ico"
            elif os.path.exists("assets/Hiel RnE Logo.png"):
                self.page.window_icon = "assets/Hiel RnE Logo.png"
        except Exception as e:
            print(f"Could not set window icon: {e}")
        
        # Setup keyboard shortcuts for enhanced features
        self.page.on_keyboard_event = self._on_keyboard_event
        
        # Create modern layout
        self._create_modern_sidebar()
        self._create_modern_topbar()
        self._create_modern_main_content()
        self._create_status_bar()
        
        # Setup modern page layout
        self.page.add(
            ft.Row([
                self.modern_sidebar.build(),
                ft.Column([
                    self.modern_topbar.build(),
                    self.modern_breadcrumb.build() if self.modern_breadcrumb else ft.Container(),
                    self.main_content,
                    self.status_bar
                ], expand=True)
            ], expand=True)
        )
        
        # NOTE: Don't add enhanced progress overlay to page.overlay during initialization
        # It will be added only when show() is called to avoid blocking interactions
        
        # Initial navigation
        self.navigate_to_tab(TabState.PROJECT_SETUP)
    
    def _create_modern_sidebar(self):
        """Create modern sidebar navigation with enhanced UX."""
        navigation_items = create_navigation_items()
        
        self.modern_sidebar = ModernSidebar(
            navigation_items=navigation_items,
            on_navigate=self._handle_modern_navigation,
            on_state_change=self._handle_sidebar_state_change,
            initial_state=NavigationState.EXPANDED,
            show_user_profile=True,
            user_name="Financial Analyst",
            user_avatar=None
        )
    
    def _create_modern_topbar(self):
        """Create modern top navigation bar."""
        # Quick action buttons
        quick_actions = [
            ModernButton(
                "New Project",
                variant=ButtonVariant.PRIMARY,
                size=ComponentSize.SM,
                icon=ft.Icons.ADD,
                on_click=self._handle_new_project
            ).build(),
            ModernButton(
                "Export",
                variant=ButtonVariant.SECONDARY,
                size=ComponentSize.SM,
                icon=ft.Icons.DOWNLOAD,
                on_click=self._handle_quick_export
            ).build()
        ]
        
        self.modern_topbar = ModernTopBar(
            title="Enhanced Financial Analysis",
            actions=quick_actions,
            show_search=True,
            on_search=self._handle_global_search
        )
    
    def _create_modern_main_content(self):
        """Create modern main content area with enhanced styling."""
        # Initialize with home breadcrumb
        self.current_breadcrumb = [
            {"label": "Home", "route": "/dashboard"},
            {"label": "Project Setup", "route": "/setup"}
        ]
        
        self.modern_breadcrumb = ModernBreadcrumb(
            items=self.current_breadcrumb,
            on_navigate=self._handle_breadcrumb_navigation
        )

        # Main content container with modern styling (single child)
        self.main_content = ft.Container(
            content=ft.Container(
                content=ft.Text("Loading enhanced interface...", 
                               text_align=ft.TextAlign.CENTER,
                               size=16,
                               color=self.modern_theme.get_text_colors()['secondary']),
                expand=True,
                padding=self.modern_theme.tokens.spacing['xxl'],
                bgcolor=self.modern_theme.get_background_colors()['primary']
            ),
            expand=True
        )
    
    def _create_status_bar(self):
        """Create enhanced status bar with health monitoring."""
        self.progress_bar = ft.ProgressBar(visible=False)

        # Health status indicator
        self.health_indicator = ft.Icon(
            ft.Icons.HEALTH_AND_SAFETY,
            color=ft.Colors.GREEN,
            size=16,
            tooltip="System Health: All services operational"
        )

        # Enhanced features indicators
        self.enhanced_indicators = ft.Row([
            ft.Icon(ft.Icons.MEMORY, size=14, color=ft.Colors.BLUE_600, tooltip="ML Predictions Active"),
            ft.Icon(ft.Icons.VIEW_IN_AR, size=14, color=ft.Colors.PURPLE_600, tooltip="3D Charts Available"),
            ft.Icon(ft.Icons.SAVE, size=14, color=ft.Colors.GREEN_600, tooltip="Auto-Save Enabled"),
            ft.Icon(ft.Icons.UNDO, size=14, color=ft.Colors.ORANGE_600, tooltip="Undo/Redo Available")
        ], spacing=4, visible=True)

        self.status_bar = ft.Container(
            content=ft.Column([
                self.progress_bar,
                ft.Row([
                    ft.Row([
                        ft.Text("Ready", size=12, color=ft.Colors.GREY_600),
                        self.health_indicator,
                        ft.VerticalDivider(width=1),
                        self.enhanced_indicators
                    ], spacing=8),
                    ft.Text("Hiel RnE Modeler v3.0 • Agevolami SRL • 🚀 All Advanced Features Active",
                           size=12, color=ft.Colors.GREY_600, expand=True,
                           text_align=ft.TextAlign.RIGHT)
                ])
            ]),
            padding=ft.padding.symmetric(horizontal=20, vertical=10),
            bgcolor=ft.Colors.GREY_100,
            height=60
        )

        # Start periodic health checks
        self.page.run_task(self._periodic_health_check)
    
    def _on_keyboard_event(self, e: ft.KeyboardEvent):
        """Handle keyboard shortcuts for enhanced features."""
        try:
            # Undo: Ctrl+Z
            if e.key == "Z" and e.ctrl and not e.shift:
                if self.enhanced_service.undo_redo_service:
                    result = self.enhanced_service.undo_redo_service.undo()
                    if result is not None:
                        self.show_success("Action undone")
                        self.refresh_current_view()
                    else:
                        self.show_error("Nothing to undo")
                e.prevent_default = True
            
            # Redo: Ctrl+Shift+Z or Ctrl+Y
            elif ((e.key == "Z" and e.ctrl and e.shift) or 
                  (e.key == "Y" and e.ctrl)):
                if self.enhanced_service.undo_redo_service:
                    result = self.enhanced_service.undo_redo_service.redo()
                    if result is not None:
                        self.show_success("Action redone")
                        self.refresh_current_view()
                    else:
                        self.show_error("Nothing to redo")
                e.prevent_default = True
            
            # Save: Ctrl+S
            elif e.key == "S" and e.ctrl:
                if self.enhanced_service.persistence_service:
                    project_id = self.app_state.client_profile.get_clean_company_name()
                    version_id = self.enhanced_service.save_project_with_versioning(
                        project_id=project_id,
                        project_data={
                            'client_profile': self.app_state.client_profile.to_dict(),
                            'assumptions': self.app_state.project_assumptions.to_dict(),
                            'results': self.app_state.financial_results
                        }
                    )
                    self.show_success(f"Project saved (version: {version_id})")
                e.prevent_default = True
                
        except Exception as ex:
            self.logger.error(f"Keyboard shortcut error: {ex}")
    
    def refresh_current_view(self):
        """Refresh the currently active view."""
        current_tab = self.ui_state.current_tab
        if current_tab in self.views:
            view = self.views[current_tab]
            if hasattr(view, 'refresh'):
                view.refresh()
    
    def _handle_modern_navigation(self, route: str):
        """Handle modern sidebar navigation."""
        # Map routes to tab states
        route_mapping = {
            "/dashboard": TabState.DASHBOARD,
            "/setup": TabState.PROJECT_SETUP,
            "/locations": TabState.LOCATION_COMPARISON,
            "/analysis": TabState.FINANCIAL_MODEL,  # Default analysis view
            "/analysis/financial": TabState.FINANCIAL_MODEL,
            "/analysis/sensitivity": TabState.SENSITIVITY,
            "/analysis/monte_carlo": TabState.MONTE_CARLO,
            "/analysis/scenarios": TabState.SCENARIOS,
            "/validation": TabState.VALIDATION,
            "/export": TabState.EXPORT
        }
        
        selected_tab = route_mapping.get(route, TabState.PROJECT_SETUP)
        self.navigate_to_tab(selected_tab)
        
        # Update breadcrumbs
        self._update_modern_breadcrumbs(route)
    
    def _handle_sidebar_state_change(self, state: NavigationState):
        """Handle sidebar state changes."""
        self.logger.info(f"Sidebar state changed to: {state.value}")
        # Could trigger page layout adjustments here
    
    def _handle_breadcrumb_navigation(self, route: str):
        """Handle breadcrumb navigation clicks."""
        self._handle_modern_navigation(route)
    
    def _handle_new_project(self, _):
        """Handle new project button click."""
        # Clear current project data
        self.app_state.reset()
        self.navigate_to_tab(TabState.PROJECT_SETUP)
        self.notification_system.show_success("Started new project")
    
    def _handle_quick_export(self, _):
        """Handle quick export button click."""
        self.navigate_to_tab(TabState.EXPORT)
    
    def _handle_global_search(self, query: str):
        """Handle global search functionality."""
        if query.strip():
            self.logger.info(f"Global search: {query}")
            # TODO: Implement search functionality
            self.notification_system.show_info(f"Searching for: {query}")
    
    def _update_modern_breadcrumbs(self, route: str):
        """Update breadcrumb navigation based on current route."""
        breadcrumb_map = {
            "/dashboard": [
                {"label": "Home", "route": "/dashboard"}
            ],
            "/setup": [
                {"label": "Home", "route": "/dashboard"},
                {"label": "Project Setup", "route": "/setup"}
            ],
            "/locations": [
                {"label": "Home", "route": "/dashboard"},
                {"label": "Location Analysis", "route": "/locations"}
            ],
            "/analysis": [
                {"label": "Home", "route": "/dashboard"},
                {"label": "Analysis", "route": "/analysis"}
            ],
            "/analysis/financial": [
                {"label": "Home", "route": "/dashboard"},
                {"label": "Analysis", "route": "/analysis"},
                {"label": "Financial Model", "route": "/analysis/financial"}
            ],
            "/analysis/sensitivity": [
                {"label": "Home", "route": "/dashboard"},
                {"label": "Analysis", "route": "/analysis"},
                {"label": "Sensitivity Analysis", "route": "/analysis/sensitivity"}
            ],
            "/analysis/monte_carlo": [
                {"label": "Home", "route": "/dashboard"},
                {"label": "Analysis", "route": "/analysis"},
                {"label": "Monte Carlo", "route": "/analysis/monte_carlo"}
            ],
            "/analysis/scenarios": [
                {"label": "Home", "route": "/dashboard"},
                {"label": "Analysis", "route": "/analysis"},
                {"label": "Scenarios", "route": "/analysis/scenarios"}
            ],
            "/validation": [
                {"label": "Home", "route": "/dashboard"},
                {"label": "Validation", "route": "/validation"}
            ],
            "/export": [
                {"label": "Home", "route": "/dashboard"},
                {"label": "Export & Reports", "route": "/export"}
            ]
        }
        
        self.current_breadcrumb = breadcrumb_map.get(route, [{"label": "Home", "route": "/dashboard"}])
        
        if self.modern_breadcrumb:
            self.modern_breadcrumb.items = self.current_breadcrumb
    
    def navigate_to_tab(self, tab: TabState):
        """Navigate to a specific tab with modern visual feedback."""
        if not self.ui_state.can_navigate_to_tab(tab):
            self.show_error("Please complete the required steps before accessing this tab")
            return
        self.set_loading(True, "Loading...")
        self.ui_state.navigate_to_tab(tab)
        tab_to_route = {
            TabState.DASHBOARD: "/dashboard",
            TabState.PROJECT_SETUP: "/setup", 
            TabState.LOCATION_COMPARISON: "/locations",
            TabState.FINANCIAL_MODEL: "/analysis/financial",
            TabState.SENSITIVITY: "/analysis/sensitivity",
            TabState.MONTE_CARLO: "/analysis/monte_carlo",
            TabState.SCENARIOS: "/analysis/scenarios",
            TabState.VALIDATION: "/validation",
            TabState.EXPORT: "/export"
        }
        route = tab_to_route.get(tab, "/setup")
        if self.modern_sidebar:
            route_to_nav_id = {
                "/dashboard": "dashboard",
                "/setup": "setup",
                "/locations": "locations",
                "/analysis": "analysis", 
                "/analysis/financial": "financial",
                "/analysis/sensitivity": "sensitivity",
                "/analysis/monte_carlo": "monte_carlo",
                "/analysis/scenarios": "scenarios",
                "/validation": "validation",
                "/export": "export"
            }
            nav_id = route_to_nav_id.get(route, "setup")
            self.modern_sidebar.select_item(nav_id)
        self._update_modern_breadcrumbs(route)
        # Update main content with real view
        if tab in self.views:
            view = self.views[tab]
            content = view.get_content()
            self.main_content.content = ft.Container(
                content=content,
                expand=True,
                bgcolor=self.modern_theme.get_background_colors()['primary']
            )
        else:
            self.main_content.content = ft.Container(
                content=ft.Text(
                    f"View for {tab.value} not implemented yet",
                    text_align=ft.TextAlign.CENTER,
                    size=16,
                    color=ft.Colors.GREY_600
                ),
                expand=True,
                bgcolor=self.modern_theme.get_background_colors()['primary']
            )
        self.page.update()
        self.logger.info(f"Navigated to tab: {tab.value}")


    
    def handle_data_change(self, data_type: str, data: Any):
        """Handle data changes from views."""
        if data_type == "client_profile":
            self.app_state.client_profile = data
        elif data_type == "project_assumptions":
            self.app_state.project_assumptions = data
        
        self.logger.info(f"Data changed: {data_type}")
    
    def handle_action_request(self, action: str, params: Dict[str, Any]):
        """Handle action requests from views."""
        self.logger.info(f"Action requested: {action}")
        
        try:
            if action == "run_financial_model":
                self.page.run_task(self._run_financial_model)
            elif action == "run_comprehensive_analysis":
                self.page.run_task(self._run_comprehensive_analysis, params)
            elif action == "run_comprehensive_analysis_with_locations":
                self.page.run_task(self._run_comprehensive_analysis_with_locations, params)
            elif action == "run_location_comparison":
                self.page.run_task(self._run_location_comparison, params)
            elif action == "run_sensitivity_analysis":
                self.page.run_task(self._run_sensitivity_analysis, params)
            elif action == "run_monte_carlo":
                self.page.run_task(self._run_monte_carlo, params)
            elif action == "run_scenario_analysis":
                self.page.run_task(self._run_scenario_analysis, params)
            elif action == "quick_export":
                self.page.run_task(self._quick_export, params)
            elif action == "comprehensive_export":
                self.page.run_task(self._comprehensive_export, params)
            elif action == "save_configuration":
                self._save_configuration(params)
            elif action == "load_preset":
                self._load_preset()
            else:
                self.show_error(f"Unknown action: {action}")
        
        except Exception as e:
            self.logger.error(f"Error handling action {action}: {str(e)}")
            self.show_error(f"Error: {str(e)}")
    
    async def _run_financial_model(self):
        """Run the financial model."""
        try:
            self.set_loading(True, "Running financial model...")
            
            def progress_callback(progress: float, message: str):
                # Use thread-safe progress update
                self._safe_progress_update(progress, message)
            
            # Run financial model
            results = self.financial_service.run_financial_model(
                self.app_state.project_assumptions,
                progress_callback
            )
            
            # Update state
            self.app_state.financial_results = results
            self.ui_state.set_model_executed(True)
            
            # Update views with results
            self._update_views_with_results()
            
            self.set_loading(False)
            self.show_success("Financial model completed successfully!")
            
        except Exception as e:
            self.set_loading(False)
            self.show_error(f"Error running financial model: {str(e)}")
    
    async def _run_comprehensive_analysis(self, params: Dict[str, Any]):
        """Run enhanced comprehensive analysis with all advanced features."""
        try:
            # Debug: Check if enhanced progress overlay exists
            if not hasattr(self, 'enhanced_progress_overlay') or not self.enhanced_progress_overlay:
                self.logger.error("Enhanced progress overlay not available!")
                self.set_loading(True, "Running comprehensive analysis...")
                # Continue with standard loading...
            else:
                self.logger.info("Using enhanced progress overlay for comprehensive analysis")
                # Use enhanced progress overlay
                await self.enhanced_progress_overlay.show("Starting enhanced comprehensive analysis...")
                # Force a small delay to ensure overlay shows
                await asyncio.sleep(0.1)

            def progress_callback(progress: float, message: str):
                """Enhanced progress callback with thread-safe UI updates."""
                # Update enhanced progress overlay with step tracking
                total_steps = 11  # Total number of major steps
                current_step = int(progress / 10) + 1 if progress < 100 else total_steps
                
                self.logger.info(f"Progress updated: {progress}% - {message}")
                
                # Use thread-safe UI update helper
                self._safe_progress_update(progress, message, current_step, total_steps)

            # Step 1: Run enhanced financial model with ML predictions and Monte Carlo
            progress_callback(10, "Running enhanced financial model with ML predictions...")

            project_data = {
                'client_profile': self.app_state.client_profile.to_dict(),
                'assumptions': self.app_state.project_assumptions.to_dict()
            }

            # Ensure enhanced service is available
            if not hasattr(self, 'enhanced_service') or not self.enhanced_service:
                self.logger.error("Enhanced service not available, using fallback analysis")
                # Run fallback analysis
                progress_callback(50, "Running standard financial analysis...")
                
                standard_results = self.report_service.generate_comprehensive_report(
                    client_profile=self.app_state.client_profile,
                    assumptions=self.app_state.project_assumptions,
                    progress_callback=lambda p, m: progress_callback(50 + p*0.5, m)
                )
                
                enhanced_results = standard_results['analysis_results'].get('financial', {})
                charts_3d = {}
            else:
                # Normal enhanced analysis
                enhanced_results = self.enhanced_service.run_enhanced_financial_model(
                    project_data=project_data,
                    include_ml_predictions=True,
                    include_monte_carlo=True
                )

                # CRITICAL FIX: Update app state with enhanced financial results immediately
                # This ensures the dashboard gets the financial data
                self.app_state.financial_results = enhanced_results
                
                # Step 2: Generate 3D charts
                progress_callback(40, "Generating interactive 3D visualizations...")
                
                charts_3d = self.enhanced_service.generate_advanced_charts(
                    financial_results=enhanced_results,
                    project_name=self.app_state.client_profile.project_name or "Solar Project"
                )
                
                # Step 3: Auto-save with versioning
                progress_callback(60, "Saving project with versioning...")
                
                if self.enhanced_service.persistence_service:
                    project_id = self.app_state.client_profile.get_clean_company_name()
                    version_id = self.enhanced_service.save_project_with_versioning(
                        project_id=project_id,
                        project_data={
                            'client_profile': self.app_state.client_profile.to_dict(),
                            'assumptions': self.app_state.project_assumptions.to_dict(),
                            'enhanced_results': enhanced_results,
                            'charts_3d': charts_3d
                        }
                    )
                    self.logger.info(f"Project saved with version: {version_id}")
                
                # Step 4: Run standard comprehensive report for exports
                progress_callback(70, "Generating comprehensive reports...")
                
                try:
                    standard_results = self.report_service.generate_comprehensive_report(
                        client_profile=self.app_state.client_profile,
                        assumptions=self.app_state.project_assumptions,
                        progress_callback=lambda p, m: progress_callback(70 + p*0.25, m)
                    )
                except Exception as e:
                    self.logger.error(f"Error generating comprehensive report: {e}")
                    # Create fallback standard results structure
                    standard_results = {
                        'analysis_results': {
                            'validation': {},
                            'location_comparison': {},
                            'sensitivity': {},
                            'scenarios': {}
                        },
                        'generated_files': []
                    }
            
            # Step 5: Merge enhanced and standard results
            progress_callback(95, "Finalizing enhanced results...")

            # Update state with enhanced results (financial_results already updated above)
            self.app_state.validation_results = standard_results['analysis_results'].get('validation')
            self.app_state.location_comparison_results = standard_results['analysis_results'].get('location_comparison')
            self.app_state.sensitivity_results = standard_results['analysis_results'].get('sensitivity')
            self.app_state.monte_carlo_results = enhanced_results.get('monte_carlo')
            self.app_state.scenario_results = standard_results['analysis_results'].get('scenarios')

            # Store enhanced features
            self.app_state.ml_predictions = enhanced_results.get('ml_predictions')
            self.app_state.charts_3d = charts_3d

            # Update UI state
            self.ui_state.set_model_executed(True)

            # CRITICAL FIX: Force refresh dashboard with all data at once
            # This ensures the dashboard shows the KPIs and financial data immediately
            if TabState.DASHBOARD in self.views and self.app_state.financial_results:
                dashboard_view = self.views[TabState.DASHBOARD]
                if hasattr(dashboard_view, 'force_refresh_with_data'):
                    dashboard_view.force_refresh_with_data(
                        financial_results=self.app_state.financial_results,
                        ml_predictions=enhanced_results.get('ml_predictions'),
                        charts_3d=charts_3d
                    )
                else:
                    dashboard_view.set_financial_results(self.app_state.financial_results)
                self.logger.info("Dashboard force refreshed with comprehensive analysis results")

            # Update views with enhanced data
            self._update_views_with_enhanced_results(enhanced_results, charts_3d)
            
            # CRITICAL FIX: Generate enhanced interactive dashboard with proper data structure
            progress_callback(96, "Generating enhanced interactive dashboard...")
            try:
                # Create properly structured analysis results for interactive dashboard
                enhanced_analysis_results = {
                    'financial': enhanced_results,  # This is the key fix!
                    'validation': standard_results['analysis_results'].get('validation'),
                    'location_comparison': standard_results['analysis_results'].get('location_comparison'),
                    'sensitivity': standard_results['analysis_results'].get('sensitivity'),
                    'monte_carlo': enhanced_results.get('monte_carlo'),
                    'scenarios': standard_results['analysis_results'].get('scenarios'),
                    'enhanced_features': {
                        'ml_predictions': enhanced_results.get('ml_predictions'),
                        'charts_3d': charts_3d
                    }
                }

                # Generate enhanced interactive dashboard
                dashboard_file = self.export_service.export_interactive_dashboard(
                    client_profile=self.app_state.client_profile,
                    assumptions=self.app_state.project_assumptions,
                    analysis_results=enhanced_analysis_results
                )

                # Add to generated files
                standard_results['generated_files'].append(('Enhanced Interactive Dashboard', dashboard_file))
                self.logger.info("Enhanced interactive dashboard generated successfully")

            except Exception as e:
                self.logger.error(f"Failed to generate enhanced interactive dashboard: {e}")

            # Update export view with generated files
            if TabState.EXPORT in self.views:
                export_view = self.views[TabState.EXPORT]
                for file_type, file_path in standard_results['generated_files']:
                    export_view.add_generated_file(file_type, file_path)
            
            progress_callback(100, "Enhanced analysis completed!")
            
            # Add a small delay to ensure the 100% progress is visible
            await asyncio.sleep(1)
            
            # Ensure overlay is properly hidden and removed with final message
            if hasattr(self, 'enhanced_progress_overlay') and self.enhanced_progress_overlay:
                await self.enhanced_progress_overlay.hide(final_message="Done")
            
            # Enhanced success message
            ml_status = "✓" if enhanced_results.get('ml_predictions') else "✗"
            charts_3d_count = len([k for k in charts_3d.keys() if '3d' in k.lower()])
            cache_status = "✓" if self.enhanced_service.cache_service else "✗"
            
            success_msg = (f"🚀 Enhanced Analysis Completed!\n\n"
                          f"📊 Features Used:\n"
                          f"  {ml_status} ML Predictions: {len(enhanced_results.get('ml_predictions', {}).get('predictions', {}))}/3 models\n"
                          f"  ✓ 3D Charts: {charts_3d_count} interactive visualizations\n"
                          f"  {cache_status} Performance Cache: {'Enabled' if self.enhanced_service.cache_service else 'Disabled'}\n"
                          f"  ✓ Auto-Save: Project versioned and backed up\n"
                          f"  ✓ Error Recovery: Comprehensive fallback systems\n\n"
                          f"📁 Generated: {len(standard_results['generated_files'])} files")
            
            self.show_success(success_msg)
            
        except Exception as e:
            # Ensure overlay is hidden on error
            if hasattr(self, 'enhanced_progress_overlay') and self.enhanced_progress_overlay:
                await self.enhanced_progress_overlay.hide()
            self.logger.error(f"Enhanced analysis failed: {e}")
            
            # Enhanced error recovery
            if self.enhanced_service.recovery_service:
                try:
                    fallback_results = self.enhanced_service._get_fallback_results()
                    self.app_state.financial_results = fallback_results
                    self.show_error(f"Analysis failed, using fallback data: {str(e)}")
                except:
                    self.show_error(f"Error in enhanced comprehensive analysis: {str(e)}")
            else:
                self.show_error(f"Error in enhanced comprehensive analysis: {str(e)}")
    
    async def _run_comprehensive_analysis_with_locations(self, params: Dict[str, Any]):
        """Run comprehensive analysis with location intelligence integration."""
        try:
            # Extract location parameters
            selected_locations = params.get('selected_locations', [])
            include_comparison = params.get('include_location_comparison', False)
            analysis_context = params.get('analysis_context', {})
            
            self.logger.info(f"Starting location-enhanced analysis for {len(selected_locations)} locations")
            
            # Use enhanced progress overlay
            if hasattr(self, 'enhanced_progress_overlay') and self.enhanced_progress_overlay:
                await self.enhanced_progress_overlay.show("Starting location-intelligent analysis...")
                await asyncio.sleep(0.1)
            else:
                self.set_loading(True, "Running location-enhanced analysis...")

            def progress_callback(progress: float, message: str):
                """Enhanced progress callback with location context."""
                total_steps = 12 if include_comparison else 10
                current_step = int(progress / (100/total_steps)) + 1 if progress < 100 else total_steps
                
                self.logger.info(f"Location Analysis Progress: {progress}% - {message}")
                self._safe_progress_update(progress, message, current_step, total_steps)

            # Step 1: If multiple locations selected, run location comparison first
            if include_comparison and len(selected_locations) > 1:
                progress_callback(10, f"Comparing {len(selected_locations)} locations...")
                
                try:
                    comparison_results = self.location_service.compare_locations(
                        self.app_state.project_assumptions,
                        selected_locations
                    )
                    
                    # Store comparison results
                    self.app_state.location_comparison_results = comparison_results
                    
                    # Update location comparison view
                    if TabState.LOCATION_COMPARISON in self.views:
                        self.views[TabState.LOCATION_COMPARISON].set_comparison_results(comparison_results)
                    
                    # Get optimal location from comparison
                    optimal_location = comparison_results.get('analysis', {}).get('recommendations', {}).get('best_overall', {}).get('location')
                    if optimal_location:
                        progress_callback(20, f"Identified optimal location: {optimal_location}")
                        # Focus analysis on optimal location
                        selected_locations = [optimal_location]
                    else:
                        # Fallback to first selected location
                        selected_locations = [selected_locations[0]]
                        progress_callback(20, f"Using primary location: {selected_locations[0]}")
                    
                except Exception as e:
                    self.logger.warning(f"Location comparison failed, proceeding with single location: {e}")
                    selected_locations = [selected_locations[0]]
                    progress_callback(20, f"Proceeding with single location analysis")
            else:
                progress_callback(15, f"Single location analysis: {selected_locations[0] if selected_locations else 'Default'}")

            # Step 2: Update project assumptions with optimal location data
            if selected_locations:
                primary_location = selected_locations[0]
                progress_callback(25, f"Configuring project for {primary_location}...")
                
                # Here you could update project assumptions based on location-specific data
                # For example, adjust CAPEX, OPEX, or capacity factors based on location
                self.logger.info(f"Primary analysis location set to: {primary_location}")
            
            # Step 3: Run enhanced comprehensive analysis
            progress_callback(30, "Running enhanced financial model with location optimization...")
            
            # Prepare enhanced project data with location context
            project_data = {
                'client_profile': self.app_state.client_profile.to_dict(),
                'assumptions': self.app_state.project_assumptions.to_dict(),
                'location_context': {
                    'primary_location': selected_locations[0] if selected_locations else None,
                    'selected_locations': selected_locations,
                    'comparison_enabled': include_comparison,
                    'analysis_context': analysis_context
                }
            }

            # Run enhanced financial model with location intelligence
            if hasattr(self, 'enhanced_service') and self.enhanced_service:
                enhanced_results = self.enhanced_service.run_enhanced_financial_model(
                    project_data=project_data,
                    include_ml_predictions=True,
                    include_monte_carlo=True
                )
                
                # Update app state
                self.app_state.financial_results = enhanced_results
                
                # Step 4: Generate 3D charts with location context
                progress_callback(60, "Generating location-aware 3D visualizations...")
                
                project_name = f"{self.app_state.client_profile.project_name or 'Solar Project'} ({selected_locations[0] if selected_locations else 'Multi-Location'})"
                charts_3d = self.enhanced_service.generate_advanced_charts(
                    financial_results=enhanced_results,
                    project_name=project_name
                )
                
                # Step 5: Auto-save with location data
                progress_callback(75, "Saving project with location intelligence...")
                
                if self.enhanced_service.persistence_service:
                    project_id = self.app_state.client_profile.get_clean_company_name()
                    version_id = self.enhanced_service.save_project_with_versioning(
                        project_id=project_id,
                        project_data={
                            'client_profile': self.app_state.client_profile.to_dict(),
                            'assumptions': self.app_state.project_assumptions.to_dict(),
                            'enhanced_results': enhanced_results,
                            'location_data': {
                                'selected_locations': selected_locations,
                                'comparison_results': self.app_state.location_comparison_results,
                                'primary_location': selected_locations[0] if selected_locations else None
                            },
                            'charts_3d': charts_3d
                        }
                    )
                    self.logger.info(f"Location-enhanced project saved with version: {version_id}")
            else:
                # Fallback to standard analysis
                progress_callback(50, "Running standard analysis (enhanced features unavailable)...")
                
                results = self.financial_service.run_financial_model(
                    self.app_state.project_assumptions,
                    lambda p, m: progress_callback(50 + p*0.3, m)
                )
                
                enhanced_results = results
                charts_3d = {}
                self.app_state.financial_results = results

            # Step 6: Generate comprehensive reports
            progress_callback(85, "Generating location-enhanced reports...")
            
            try:
                standard_results = self.report_service.generate_comprehensive_report(
                    client_profile=self.app_state.client_profile,
                    assumptions=self.app_state.project_assumptions,
                    progress_callback=lambda p, m: progress_callback(85 + p*0.1, m)
                )
            except Exception as e:
                self.logger.error(f"Error generating location-enhanced report: {e}")
                # Create fallback standard results structure
                standard_results = {
                    'analysis_results': {
                        'validation': {},
                        'location_comparison': {},
                        'sensitivity': {},
                        'scenarios': {}
                    },
                    'generated_files': []
                }
            
            # Update app state with all results
            self.app_state.validation_results = standard_results['analysis_results'].get('validation')
            self.app_state.sensitivity_results = standard_results['analysis_results'].get('sensitivity')
            self.app_state.monte_carlo_results = enhanced_results.get('monte_carlo')
            self.app_state.scenario_results = standard_results['analysis_results'].get('scenarios')
            
            # Store enhanced features
            self.app_state.ml_predictions = enhanced_results.get('ml_predictions')
            self.app_state.charts_3d = charts_3d
            
            # Update UI state
            self.ui_state.set_model_executed(True)
            
            # Step 7: Update all views with location-enhanced results
            progress_callback(95, "Updating dashboard with location intelligence...")
            
            # Force refresh dashboard with comprehensive data
            if TabState.DASHBOARD in self.views and self.app_state.financial_results:
                dashboard_view = self.views[TabState.DASHBOARD]
                if hasattr(dashboard_view, 'force_refresh_with_data'):
                    dashboard_view.force_refresh_with_data(
                        financial_results=self.app_state.financial_results,
                        ml_predictions=enhanced_results.get('ml_predictions'),
                        charts_3d=charts_3d
                    )
                else:
                    dashboard_view.set_financial_results(self.app_state.financial_results)
                self.logger.info("Dashboard updated with location-enhanced analysis results")

            # Update views with enhanced data
            self._update_views_with_enhanced_results(enhanced_results, charts_3d)
            
            # Update export view
            if TabState.EXPORT in self.views:
                export_view = self.views[TabState.EXPORT]
                for file_type, file_path in standard_results['generated_files']:
                    export_view.add_generated_file(file_type, file_path)
            
            progress_callback(100, "Location-enhanced analysis completed!")
            
            # Add delay to ensure progress is visible
            await asyncio.sleep(1)
            
            # Hide progress overlay
            if hasattr(self, 'enhanced_progress_overlay') and self.enhanced_progress_overlay:
                await self.enhanced_progress_overlay.hide(final_message="Location analysis complete!")
            else:
                self.set_loading(False)
            
            # Enhanced success message with location context
            location_info = f"{len(selected_locations)} location(s)" if selected_locations else "default configuration"
            comparison_info = " with comparison analysis" if include_comparison else ""
            primary_location_info = f" (Optimal: {selected_locations[0]})" if selected_locations else ""
            
            ml_status = "✓" if enhanced_results.get('ml_predictions') else "✗"
            charts_3d_count = len([k for k in charts_3d.keys() if '3d' in k.lower()]) if charts_3d else 0
            
            success_msg = (f"🌍 Location-Enhanced Analysis Completed!\n\n"
                          f"📍 Location Intelligence:\n"
                          f"  ✓ Analyzed: {location_info}{comparison_info}{primary_location_info}\n"
                          f"  ✓ Location Data: Integrated into financial model\n"
                          f"  {'✓' if include_comparison else '○'} Comparison: {'Multi-location optimization' if include_comparison else 'Single location focus'}\n\n"
                          f"🚀 Enhanced Features:\n"
                          f"  {ml_status} ML Predictions: {len(enhanced_results.get('ml_predictions', {}).get('predictions', {})) if enhanced_results.get('ml_predictions') else 0}/3 models\n"
                          f"  ✓ 3D Charts: {charts_3d_count} location-aware visualizations\n"
                          f"  ✓ Auto-Save: Project with location data saved\n\n"
                          f"📁 Generated: {len(standard_results['generated_files'])} files")
            
            self.show_success(success_msg)
            
        except Exception as e:
            # Ensure overlay is hidden on error
            if hasattr(self, 'enhanced_progress_overlay') and self.enhanced_progress_overlay:
                await self.enhanced_progress_overlay.hide()
            else:
                self.set_loading(False)
                
            self.logger.error(f"Location-enhanced analysis failed: {e}")
            self.show_error(f"Error in location-enhanced analysis: {str(e)}")
    
    async def _run_location_comparison(self, params: Dict[str, Any]):
        """Run location comparison analysis."""
        try:
            self.set_loading(True, "Running location comparison...")
            
            locations = params.get('locations', [])
            results = self.location_service.compare_locations(
                self.app_state.project_assumptions,
                locations
            )
            
            self.app_state.location_comparison_results = results
            
            # Update location comparison view
            if TabState.LOCATION_COMPARISON in self.views:
                self.views[TabState.LOCATION_COMPARISON].set_comparison_results(results)
            
            self.set_loading(False)
            self.show_success("Location comparison completed!")
            
        except Exception as e:
            self.set_loading(False)
            self.show_error(f"Error in location comparison: {str(e)}")
    
    async def _run_sensitivity_analysis(self, params: Dict[str, Any]):
        """Run sensitivity analysis."""
        try:
            self.set_loading(True, "Running sensitivity analysis...")
            
            variables = params.get('variables', [])
            results = self.financial_service.run_sensitivity_analysis(
                self.app_state.project_assumptions,
                variables
            )
            
            self.app_state.sensitivity_results = results
            
            # Update sensitivity view
            if TabState.SENSITIVITY in self.views:
                self.views[TabState.SENSITIVITY].set_sensitivity_results(results)
            
            self.set_loading(False)
            self.show_success("Sensitivity analysis completed!")
            
        except Exception as e:
            self.set_loading(False)
            self.show_error(f"Error in sensitivity analysis: {str(e)}")
    
    async def _run_monte_carlo(self, params: Dict[str, Any]):
        """Run Monte Carlo simulation."""
        try:
            self.set_loading(True, "Running Monte Carlo simulation...")
            
            n_simulations = params.get('n_simulations', 1000)
            
            def progress_callback(progress: float, message: str):
                # Use thread-safe progress update
                self._safe_progress_update(progress, message)
                
                # Update Monte Carlo view progress using thread-safe method
                def update_monte_carlo_view():
                    try:
                        if TabState.MONTE_CARLO in self.views:
                            self.views[TabState.MONTE_CARLO].set_monte_carlo_results({
                                'progress': progress,
                                'in_progress': True
                            })
                    except Exception as e:
                        self.logger.error(f"Error updating Monte Carlo view: {e}")
                
                # Schedule view update on main thread
                try:
                    if hasattr(self.page, 'run_task'):
                        self.page.run_task(update_monte_carlo_view)
                    else:
                        update_monte_carlo_view()
                except Exception as e:
                    self.logger.error(f"Error scheduling Monte Carlo view update: {e}")
            
            results = self.financial_service.run_monte_carlo_simulation(
                self.app_state.project_assumptions,
                n_simulations,
                progress_callback
            )
            
            self.app_state.monte_carlo_results = results
            
            # Update Monte Carlo view
            if TabState.MONTE_CARLO in self.views:
                self.views[TabState.MONTE_CARLO].set_monte_carlo_results(results)
            
            self.set_loading(False)
            self.show_success(f"Monte Carlo simulation completed with {n_simulations} simulations!")
            
        except Exception as e:
            self.set_loading(False)
            self.show_error(f"Error in Monte Carlo simulation: {str(e)}")
    
    async def _run_scenario_analysis(self, params: Dict[str, Any]):
        """Run scenario analysis."""
        try:
            self.set_loading(True, "Running scenario analysis...")

            scenarios = params.get('scenarios', ["Base", "Optimistic", "Pessimistic"])

            def progress_callback(progress: float, message: str):
                # Use thread-safe progress update
                self._safe_progress_update(progress, message)

            results = self.financial_service.run_scenario_analysis(
                self.app_state.project_assumptions,
                scenarios,
                progress_callback
            )

            self.app_state.scenario_results = results

            # Update scenarios view
            if TabState.SCENARIOS in self.views:
                self.views[TabState.SCENARIOS].set_scenario_results(results)

            self.set_loading(False)
            self.show_success(f"Scenario analysis completed for {len(scenarios)} scenarios!")

        except Exception as e:
            self.set_loading(False)
            self.show_error(f"Error in scenario analysis: {str(e)}")
    
    async def _quick_export(self, params: Dict[str, Any]):
        """Perform quick export."""
        try:
            self.set_loading(True, "Exporting data...")
            
            formats = params.get('formats', ['Excel'])
            
            # Export based on selected formats
            generated_files = []
            
            if 'Excel' in formats:
                excel_file = self.export_service.export_excel_report(
                    self.app_state.client_profile,
                    self.app_state.project_assumptions,
                    self.app_state.financial_results or {}
                )
                generated_files.append(('Excel Report', excel_file))
            
            # Update export view
            if TabState.EXPORT in self.views:
                export_view = self.views[TabState.EXPORT]
                for file_type, file_path in generated_files:
                    export_view.add_generated_file(file_type, file_path)
            
            self.set_loading(False)
            self.show_success(f"Export completed! Generated {len(generated_files)} files.")
            
        except Exception as e:
            self.set_loading(False)
            self.show_error(f"Error during export: {str(e)}")
    
    async def _comprehensive_export(self, params: Dict[str, Any]):
        """Perform comprehensive export."""
        # Similar to quick export but with all analysis included
        await self._quick_export(params)
    
    def _save_configuration(self, params: Dict[str, Any]):
        """Save current configuration."""
        try:
            # Save configuration logic here
            self.show_success("Configuration saved successfully!")
        except Exception as e:
            self.show_error(f"Error saving configuration: {str(e)}")
    
    def _load_preset(self):
        """Load preset configuration."""
        try:
            # Load preset logic here
            self.show_success("Preset loaded successfully!")
        except Exception as e:
            self.show_error(f"Error loading preset: {str(e)}")
    
    def _update_views_with_results(self):
        """Update all views with current results."""
        # Update dashboard
        if TabState.DASHBOARD in self.views and self.app_state.financial_results:
            self.views[TabState.DASHBOARD].set_financial_results(self.app_state.financial_results)
        
        # Update financial model view
        if TabState.FINANCIAL_MODEL in self.views and self.app_state.financial_results:
            self.views[TabState.FINANCIAL_MODEL].set_financial_results(self.app_state.financial_results)
        
        # Update validation view
        if TabState.VALIDATION in self.views and self.app_state.validation_results:
            self.views[TabState.VALIDATION].set_validation_results(self.app_state.validation_results)
    
    def _update_views_with_enhanced_results(self, enhanced_results: Dict[str, Any], charts_3d: Dict[str, str]):
        """Update all views with enhanced results including ML predictions and 3D charts."""

        # Update all standard views first
        self._update_views_with_results()

        # Update dashboard with ML predictions and 3D charts
        if TabState.DASHBOARD in self.views:
            dashboard_view = self.views[TabState.DASHBOARD]

            # Ensure financial results are set first
            if enhanced_results and not dashboard_view.has_data():
                dashboard_view.set_financial_results(enhanced_results)
                self.logger.info("Dashboard financial results set in enhanced update")

            # Add ML predictions
            if hasattr(dashboard_view, 'set_ml_predictions') and enhanced_results.get('ml_predictions'):
                dashboard_view.set_ml_predictions(enhanced_results['ml_predictions'])
                self.logger.info("Dashboard ML predictions updated")

            # Add 3D charts
            if hasattr(dashboard_view, 'set_3d_charts') and charts_3d:
                dashboard_view.set_3d_charts(charts_3d)
                self.logger.info("Dashboard 3D charts updated")
        
        # Update financial model view with enhanced data
        if TabState.FINANCIAL_MODEL in self.views:
            financial_view = self.views[TabState.FINANCIAL_MODEL]
            if hasattr(financial_view, 'set_enhanced_results'):
                financial_view.set_enhanced_results(enhanced_results)
        
        # Update Monte Carlo view with enhanced simulation
        if TabState.MONTE_CARLO in self.views and enhanced_results.get('monte_carlo'):
            mc_view = self.views[TabState.MONTE_CARLO]
            if hasattr(mc_view, 'set_monte_carlo_results'):
                mc_view.set_monte_carlo_results(enhanced_results['monte_carlo'])
        
        # Update location comparison view
        if TabState.LOCATION_COMPARISON in self.views and self.app_state.location_comparison_results:
            location_view = self.views[TabState.LOCATION_COMPARISON]
            if hasattr(location_view, 'set_comparison_results'):
                location_view.set_comparison_results(self.app_state.location_comparison_results)
        
        self.logger.info("Views updated with enhanced results including ML predictions and 3D charts")
    
    def _safe_progress_update(self, progress: float, message: str, current_step: int = None, total_steps: int = None):
        """Thread-safe progress update helper that schedules UI updates on main thread."""
        try:
            # Create update function that will run on main thread
            def ui_update():
                try:
                    if hasattr(self, 'enhanced_progress_overlay') and self.enhanced_progress_overlay:
                        # Ensure progress overlay is initialized and visible
                        if not self.enhanced_progress_overlay.is_visible and progress < 5:
                            self.logger.info("Initializing progress overlay for first update")
                            # For initialization, we need to use the sync version
                            self.enhanced_progress_overlay._show_sync("Starting enhanced analysis...")
                        
                        # Update the progress overlay with thread-safe method
                        self.enhanced_progress_overlay.update_progress(progress, message, current_step, total_steps)
                        
                        # Log successful update
                        self.logger.debug(f"UI Progress updated: {progress}% - {message} (Step {current_step}/{total_steps})")
                        
                    else:
                        self.logger.warning("Enhanced progress overlay not available, using fallback")
                        # Fallback to standard progress
                        self.update_progress(progress, message)
                        
                except Exception as e:
                    self.logger.error(f"Error in thread-safe progress UI update: {e}")
                    # Final fallback - just log the progress
                    print(f"PROGRESS LOGGED: {progress}% - {message}")
            
            # Schedule UI update on main thread
            if hasattr(self.page, 'run_task'):
                self.page.run_task(ui_update)
            else:
                # Fallback to direct execution
                ui_update()
                
        except Exception as e:
            self.logger.error(f"Critical error in safe progress update: {e}")
            # Emergency logging fallback
            print(f"EMERGENCY PROGRESS LOG: {progress}% - {message}")
    
    def update_status(self, status: str, message: str):
        """Update status bar."""
        if self.status_bar:
            status_text = self.status_bar.content.controls[1].controls[0]
            status_text.value = message
            self.page.update()
    
    def set_loading(self, loading: bool, message: str = ""):
        """Show or hide loading message in main content area."""
        if loading:
            self.main_content.content = ft.Container(
                content=ft.Text(
                    message or "Loading...",
                    text_align=ft.TextAlign.CENTER,
                    size=16,
                    color=self.modern_theme.get_text_colors()['secondary']
                ),
                expand=True,
                padding=self.modern_theme.tokens.spacing['xxl'],
                bgcolor=self.modern_theme.get_background_colors()['primary']
            )
        else:
            # Actual content will be set by navigate_to_tab
            pass
        self.page.update()
    
    def update_progress(self, progress: float, message: str = ""):
        """Update progress using thread-safe UI updates."""
        self.ui_state.update_progress(progress, message)
        
        # Schedule UI update on main thread
        def ui_update():
            try:
                if self.progress_bar:
                    self.progress_bar.value = progress / 100.0
                
                if message:
                    self.update_status("loading", f"{message} ({progress:.0f}%)")
                
                self.page.update()
            except Exception as e:
                self.logger.error(f"Error in progress UI update: {e}")
        
        # Use thread-safe execution
        try:
            if hasattr(self.page, 'run_task'):
                self.page.run_task(ui_update)
            else:
                ui_update()
        except Exception as e:
            self.logger.error(f"Error scheduling progress update: {e}")
            # Fallback to direct update
            ui_update()
    
    def show_error(self, error: str):
        """Show error message."""
        self.ui_state.set_error(error)
        self.update_status("error", f"Error: {error}")
        
        # Show snack bar
        snack_bar = ft.SnackBar(
            content=ft.Text(error),
            bgcolor=ft.Colors.RED_400,
            open=True
        )
        self.page.snack_bar = snack_bar
        self.page.update()
    
    def show_success(self, message: str):
        """Show success message."""
        self.ui_state.clear_error()
        self.update_status("success", message)
        
        # Show snack bar
        snack_bar = ft.SnackBar(
            content=ft.Text(message),
            bgcolor=ft.Colors.GREEN_400,
            open=True
        )
        self.page.snack_bar = snack_bar
        self.page.update()

    async def _periodic_health_check(self):
        """Perform periodic health checks and update status indicator."""
        import asyncio

        while True:
            try:
                # Wait 30 seconds between checks
                await asyncio.sleep(30)

                # Get health status
                health_status = get_health_status()

                # Update health indicator based on system status
                system_status = health_status.get('system_status')

                if system_status and hasattr(system_status, 'value'):
                    status_value = system_status.value
                else:
                    status_value = str(system_status).lower() if system_status else 'unknown'

                if status_value == 'healthy':
                    self.health_indicator.color = ft.Colors.GREEN
                    self.health_indicator.name = ft.Icons.HEALTH_AND_SAFETY
                    tooltip = f"System Health: All {health_status.get('total_services', 0)} services operational"
                elif status_value == 'warning':
                    self.health_indicator.color = ft.Colors.ORANGE
                    self.health_indicator.name = ft.Icons.WARNING
                    tooltip = f"System Health: {health_status.get('warning_services', 0)} services have warnings"
                elif status_value == 'critical':
                    self.health_indicator.color = ft.Colors.RED
                    self.health_indicator.name = ft.Icons.ERROR
                    tooltip = f"System Health: {health_status.get('critical_services', 0)} services critical"
                else:
                    self.health_indicator.color = ft.Colors.GREY
                    self.health_indicator.name = ft.Icons.HELP
                    tooltip = "System Health: Status unknown"

                self.health_indicator.tooltip = tooltip

                # Update UI if page is still active
                if hasattr(self.page, 'update'):
                    self.health_indicator.update()

                self.logger.debug(f"Health check completed: {status_value}")

            except Exception as e:
                self.logger.error(f"Health check failed: {str(e)}")
                # Set error state
                if hasattr(self, 'health_indicator'):
                    self.health_indicator.color = ft.Colors.RED
                    self.health_indicator.name = ft.Icons.ERROR
                    self.health_indicator.tooltip = f"Health check failed: {str(e)}"
                    try:
                        self.health_indicator.update()
                    except:
                        pass
