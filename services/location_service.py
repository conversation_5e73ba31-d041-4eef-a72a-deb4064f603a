"""
Location Comparison Service
==========================

Enhanced service for comparing different project locations with baseline-centric approach.
Maintains financial parameters constant while adjusting technical specifications.
"""

import logging
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass

from .financial_service import FinancialModelService
from .location_config_service import LocationConfigService
from models.project_assumptions import EnhancedProjectAssumptions

@dataclass
class LocationData:
    """Legacy data structure - replaced by LocationTechnicalData but kept for compatibility."""
    name: str
    country: str
    capacity_factor: float
    irradiation: float  # kWh/m²/year
    capex_adjustment: float  # Multiplier (1.0 = baseline)
    opex_adjustment: float   # Multiplier (1.0 = baseline)
    grid_cost: float        # EUR/MW for grid connection
    regulatory_risk: float  # Risk factor (1.0 = baseline)

class LocationComparisonService:
    """Enhanced service for baseline-centric location comparison analysis."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.financial_service = FinancialModelService()
        self.location_config_service = LocationConfigService()
        
        # Keep legacy location data for backward compatibility
        self.location_data = self._initialize_legacy_location_data()
    
    def _initialize_legacy_location_data(self) -> Dict[str, LocationData]:
        """Initialize legacy location database for backward compatibility."""
        return {
            "Ouarzazate": LocationData(
                name="Ouarzazate",
                country="Morocco",
                capacity_factor=0.24,
                irradiation=2250,
                capex_adjustment=1.0,
                opex_adjustment=1.0,
                grid_cost=15000,
                regulatory_risk=1.0
            ),
            "Dakhla": LocationData(
                name="Dakhla",
                country="Morocco",
                capacity_factor=0.28,
                irradiation=2420,
                capex_adjustment=1.05,
                opex_adjustment=1.1,
                grid_cost=25000,
                regulatory_risk=1.0
            ),
            "Tarfaya": LocationData(
                name="Tarfaya",
                country="Morocco",
                capacity_factor=0.26,
                irradiation=2380,
                capex_adjustment=1.02,
                opex_adjustment=1.05,
                grid_cost=20000,
                regulatory_risk=1.0
            ),
            "Noor Midelt": LocationData(
                name="Noor Midelt",
                country="Morocco",
                capacity_factor=0.23,
                irradiation=2180,
                capex_adjustment=0.98,
                opex_adjustment=0.95,
                grid_cost=12000,
                regulatory_risk=0.95
            ),
            "Laâyoune": LocationData(
                name="Laâyoune",
                country="Morocco",
                capacity_factor=0.27,
                irradiation=2400,
                capex_adjustment=1.03,
                opex_adjustment=1.08,
                grid_cost=22000,
                regulatory_risk=1.0
            ),
            "Tan-Tan": LocationData(
                name="Tan-Tan",
                country="Morocco",
                capacity_factor=0.25,
                irradiation=2300,
                capex_adjustment=1.01,
                opex_adjustment=1.03,
                grid_cost=18000,
                regulatory_risk=1.0
            ),
            "Boujdour": LocationData(
                name="Boujdour",
                country="Morocco",
                capacity_factor=0.26,
                irradiation=2350,
                capex_adjustment=1.04,
                opex_adjustment=1.06,
                grid_cost=24000,
                regulatory_risk=1.0
            ),
            "Tata": LocationData(
                name="Tata",
                country="Morocco",
                capacity_factor=0.24,
                irradiation=2200,
                capex_adjustment=0.99,
                opex_adjustment=0.98,
                grid_cost=16000,
                regulatory_risk=1.0
            ),
            "Zagora": LocationData(
                name="Zagora",
                country="Morocco",
                capacity_factor=0.25,
                irradiation=2280,
                capex_adjustment=1.0,
                opex_adjustment=1.0,
                grid_cost=17000,
                regulatory_risk=1.0
            )
        }
    
    def compare_locations(self, 
                         base_assumptions: EnhancedProjectAssumptions,
                         comparison_locations: List[str],
                         progress_callback: Optional[Callable[[float, str], None]] = None) -> Dict[str, Any]:
        """
        Enhanced baseline-centric location comparison.
        
        Uses the current project location from base_assumptions as baseline,
        keeps all financial parameters constant, and adjusts only technical parameters.
        """
        try:
            if progress_callback:
                progress_callback(5, "Initializing baseline-centric comparison...")
            
            # Extract baseline configuration
            baseline_config = base_assumptions.to_dict()
            baseline_location = baseline_config.get('project_location', 'Ouarzazate')
            
            self.logger.info(f"Running baseline comparison: {baseline_location} vs {comparison_locations}")
            
            if progress_callback:
                progress_callback(15, f"Using {baseline_location} as baseline...")
            
            # Use enhanced location config service for comparison
            comparison_data = self.location_config_service.compare_locations_with_baseline(
                base_config=baseline_config,
                comparison_locations=comparison_locations
            )
            
            if progress_callback:
                progress_callback(40, "Running financial analysis for each location...")
            
            # Run financial analysis for each scenario
            financial_results = {}
            scenarios = comparison_data['scenarios']
            total_scenarios = len(scenarios)
            
            for i, (scenario_name, scenario_data) in enumerate(scenarios.items()):
                if progress_callback:
                    progress = 40 + (i / total_scenarios) * 40
                    progress_callback(progress, f"Analyzing {scenario_data['location']}...")
                
                try:
                    # Convert scenario config back to EnhancedProjectAssumptions
                    scenario_assumptions = EnhancedProjectAssumptions.from_dict(scenario_data['config'])
                    
                    # Run financial model
                    financial_result = self.financial_service.run_financial_model(scenario_assumptions)
                    
                    financial_results[scenario_name] = {
                        'location': scenario_data['location'],
                        'type': scenario_data['type'],
                        'config': scenario_data['config'],
                        'financial_results': financial_result,
                        'location_data': scenario_data['config'].get('location_technical_data')
                    }
                    
                except Exception as e:
                    self.logger.error(f"Error analyzing scenario {scenario_name}: {e}")
                    financial_results[scenario_name] = {
                        'location': scenario_data['location'],
                        'type': scenario_data['type'],
                        'error': str(e)
                    }
            
            if progress_callback:
                progress_callback(85, "Generating baseline comparison analysis...")
            
            # Generate enhanced comparison analysis
            analysis = self._generate_baseline_comparison_analysis(
                financial_results, 
                baseline_location, 
                comparison_data
            )
            
            if progress_callback:
                progress_callback(95, "Creating comparison summary...")
            
            # Create comprehensive results
            results = {
                'baseline_location': baseline_location,
                'comparison_locations': comparison_locations,
                'comparison_type': 'baseline_centric',
                'financial_results': financial_results,
                'analysis': analysis,
                'technical_summary': comparison_data['technical_summary'],
                'location_data': comparison_data['location_data'],
                'constant_parameters': comparison_data.get('constant_parameters', []),
                'variable_parameters': comparison_data.get('variable_parameters', []),
                'comparison_methodology': {
                    'approach': 'Baseline-centric comparison',
                    'baseline': baseline_location,
                    'constants': 'All financial parameters (PPA, grants, financing)',
                    'variables': 'Technical parameters (production, costs, degradation)'
                }
            }
            
            if progress_callback:
                progress_callback(100, "Baseline comparison completed!")
            
            self.logger.info(f"Baseline comparison completed: {baseline_location} vs {len(comparison_locations)} locations")
            return results
            
        except Exception as e:
            self.logger.error(f"Error in baseline location comparison: {e}")
            if progress_callback:
                progress_callback(100, f"Error: {str(e)}")
            raise
    
    def _generate_baseline_comparison_analysis(self, 
                                             financial_results: Dict[str, Any],
                                             baseline_location: str,
                                             comparison_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate analysis specifically for baseline-centric comparison."""
        
        analysis = {
            'comparison_matrix': [],
            'baseline_analysis': {},
            'delta_analysis': {},
            'rankings': {},
            'recommendations': {},
            'risk_assessment': {}
        }
        
        # Get baseline results
        baseline_results = financial_results.get('baseline', {})
        if 'error' in baseline_results:
            analysis['error'] = f"Baseline analysis failed: {baseline_results['error']}"
            return analysis
        
        baseline_financial = baseline_results.get('financial_results', {})
        baseline_kpis = baseline_financial.get('kpis', {})
        
        # Create baseline analysis
        analysis['baseline_analysis'] = {
            'location': baseline_location,
            'irr_project': baseline_kpis.get('IRR_project', 0),
            'irr_equity': baseline_kpis.get('IRR_equity', 0),
            'npv_project_meur': baseline_kpis.get('NPV_project', 0) / 1e6,
            'lcoe_eur_kwh': baseline_kpis.get('LCOE_eur_kwh', 0),
            'min_dscr': baseline_kpis.get('Min_DSCR', 0),
            'capacity_factor': self._calculate_capacity_factor(baseline_results['config'])
        }
        
        # Process comparison locations
        comparison_matrix = []
        delta_analysis = {}
        
        for location_name, result_data in financial_results.items():
            if location_name == 'baseline' or 'error' in result_data:
                continue
            
            location_financial = result_data.get('financial_results', {})
            location_kpis = location_financial.get('kpis', {})
            location_config = result_data.get('config', {})
            
            # Calculate metrics
            location_metrics = {
                'location': result_data['location'],
                'irr_project': location_kpis.get('IRR_project', 0),
                'irr_equity': location_kpis.get('IRR_equity', 0),
                'npv_project_meur': location_kpis.get('NPV_project', 0) / 1e6,
                'lcoe_eur_kwh': location_kpis.get('LCOE_eur_kwh', 0),
                'min_dscr': location_kpis.get('Min_DSCR', 0),
                'capacity_factor': self._calculate_capacity_factor(location_config),
                'production_mwh': location_config.get('production_mwh_year1', 0),
                'capex_meur': location_config.get('capex_meur', 0),
                'opex_keuros': location_config.get('opex_keuros_year1', 0)
            }
            
            comparison_matrix.append(location_metrics)
            
            # Calculate deltas vs baseline
            delta_analysis[result_data['location']] = {
                'irr_project_delta': location_metrics['irr_project'] - analysis['baseline_analysis']['irr_project'],
                'irr_equity_delta': location_metrics['irr_equity'] - analysis['baseline_analysis']['irr_equity'],
                'npv_delta_meur': location_metrics['npv_project_meur'] - analysis['baseline_analysis']['npv_project_meur'],
                'lcoe_delta_eur_kwh': location_metrics['lcoe_eur_kwh'] - analysis['baseline_analysis']['lcoe_eur_kwh'],
                'capacity_factor_delta': location_metrics['capacity_factor'] - analysis['baseline_analysis']['capacity_factor'],
                'production_delta_mwh': location_metrics['production_mwh'] - baseline_results['config'].get('production_mwh_year1', 0),
                'capex_delta_meur': location_metrics['capex_meur'] - baseline_results['config'].get('capex_meur', 0)
            }
        
        # Add baseline to comparison matrix
        baseline_entry = analysis['baseline_analysis'].copy()
        baseline_entry['type'] = 'baseline'
        comparison_matrix.insert(0, baseline_entry)
        
        analysis['comparison_matrix'] = comparison_matrix
        analysis['delta_analysis'] = delta_analysis
        
        # Generate rankings (excluding baseline)
        comparison_only = [m for m in comparison_matrix if m.get('type') != 'baseline']
        if comparison_only:
            analysis['rankings'] = {
                'best_irr_project': sorted(comparison_only, key=lambda x: x['irr_project'], reverse=True),
                'best_irr_equity': sorted(comparison_only, key=lambda x: x['irr_equity'], reverse=True),
                'lowest_lcoe': sorted(comparison_only, key=lambda x: x['lcoe_eur_kwh']),
                'best_npv': sorted(comparison_only, key=lambda x: x['npv_project_meur'], reverse=True),
                'highest_capacity_factor': sorted(comparison_only, key=lambda x: x['capacity_factor'], reverse=True)
            }
        
        # Generate recommendations
        analysis['recommendations'] = self._generate_baseline_recommendations(
            analysis['baseline_analysis'],
            delta_analysis,
            comparison_data
        )
        
        return analysis
    
    def _calculate_capacity_factor(self, config: Dict[str, Any]) -> float:
        """Calculate capacity factor from configuration."""
        capacity_mw = config.get('capacity_mw', 1)
        production_mwh = config.get('production_mwh_year1', 0)
        
        if capacity_mw <= 0:
            return 0.0
        
        return production_mwh / (capacity_mw * 8760)
    
    def _generate_baseline_recommendations(self, 
                                         baseline_analysis: Dict[str, Any],
                                         delta_analysis: Dict[str, Any],
                                         comparison_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate recommendations for baseline comparison."""
        
        recommendations = {
            'best_overall': None,
            'best_for_returns': None,
            'best_for_lcoe': None,
            'best_for_risk': None,
            'alternatives_better_than_baseline': [],
            'baseline_advantages': [],
            'improvement_opportunities': []
        }
        
        if not delta_analysis:
            recommendations['note'] = "No comparison locations to analyze"
            return recommendations
        
        # Find locations better than baseline
        better_alternatives = []
        for location, deltas in delta_analysis.items():
            if deltas['irr_project_delta'] > 0 or deltas['npv_delta_meur'] > 0:
                better_alternatives.append({
                    'location': location,
                    'irr_improvement': deltas['irr_project_delta'],
                    'npv_improvement': deltas['npv_delta_meur'],
                    'lcoe_improvement': -deltas['lcoe_delta_eur_kwh']  # Negative delta is improvement
                })
        
        recommendations['alternatives_better_than_baseline'] = better_alternatives
        
        # Best overall (highest IRR improvement)
        if better_alternatives:
            best_overall = max(better_alternatives, key=lambda x: x['irr_improvement'])
            recommendations['best_overall'] = {
                'location': best_overall['location'],
                'reasons': [
                    f"IRR improvement: +{best_overall['irr_improvement']:.1%}",
                    f"NPV improvement: +€{best_overall['npv_improvement']:.1f}M",
                    f"LCOE improvement: -€{abs(best_overall['lcoe_improvement']):.3f}/kWh"
                ]
            }
            
            # Get location-specific advantages
            location_data = comparison_data['location_data'].get(best_overall['location'])
            if location_data:
                recommendations['best_overall']['location_advantages'] = location_data.advantages[:3]
        
        # Best for specific criteria
        best_irr = max(delta_analysis.items(), key=lambda x: x[1]['irr_project_delta'])
        best_lcoe = min(delta_analysis.items(), key=lambda x: x[1]['lcoe_delta_eur_kwh'])
        
        recommendations['best_for_returns'] = best_irr[0]
        recommendations['best_for_lcoe'] = best_lcoe[0]
        
        # Baseline advantages (when baseline is better)
        baseline_advantages = []
        for location, deltas in delta_analysis.items():
            if deltas['irr_project_delta'] < 0:
                advantage = f"Better IRR than {location} by {abs(deltas['irr_project_delta']):.1%}"
                baseline_advantages.append(advantage)
        
        recommendations['baseline_advantages'] = baseline_advantages[:3]  # Top 3
        
        # Improvement opportunities
        opportunities = []
        avg_production_delta = sum(d['production_delta_mwh'] for d in delta_analysis.values()) / len(delta_analysis)
        if avg_production_delta > 0:
            opportunities.append(f"Consider locations with higher solar resource (+{avg_production_delta:.0f} MWh/year average)")
        
        avg_lcoe_delta = sum(d['lcoe_delta_eur_kwh'] for d in delta_analysis.values()) / len(delta_analysis)
        if avg_lcoe_delta < 0:
            opportunities.append(f"Potential for lower LCOE (-€{abs(avg_lcoe_delta):.3f}/kWh average)")
        
        recommendations['improvement_opportunities'] = opportunities
        
        return recommendations
    
    # Legacy methods for backward compatibility
    
    def get_location_data(self, location_name: str) -> Optional[LocationData]:
        """Get legacy location data for backward compatibility."""
        return self.location_data.get(location_name)
    
    def get_available_locations(self) -> List[str]:
        """Get list of available locations."""
        # Combine legacy and new location data
        legacy_locations = list(self.location_data.keys())
        new_locations = self.location_config_service.get_available_locations()
        
        # Merge and deduplicate
        all_locations = list(set(legacy_locations + new_locations))
        return sorted(all_locations)
    
    def _create_location_assumptions(self, 
                                   base_assumptions: EnhancedProjectAssumptions,
                                   location_name: str) -> EnhancedProjectAssumptions:
        """Create modified assumptions for a specific location (legacy method)."""
        
        # Try new location config service first
        location_config = self.location_config_service.get_location_data(location_name)
        if location_config:
            scenario_config = self.location_config_service.create_location_scenario(
                base_assumptions.to_dict(),
                location_name
            )
            return EnhancedProjectAssumptions.from_dict(scenario_config)
        
        # Fallback to legacy method
        location_data = self.get_location_data(location_name)
        if not location_data:
            self.logger.warning(f"Location {location_name} not found, using base assumptions")
            return base_assumptions
        
        # Apply legacy adjustments
        adjusted_production = base_assumptions.production_mwh_year1 * (location_data.capacity_factor / 0.24)  # Normalize to Ouarzazate
        adjusted_capex = base_assumptions.capex_meur * location_data.capex_adjustment
        adjusted_opex = base_assumptions.opex_keuros_year1 * location_data.opex_adjustment
        
        return base_assumptions.copy_with_modifications(
            production_mwh_year1=adjusted_production,
            capex_meur=adjusted_capex,
            opex_keuros_year1=adjusted_opex,
            project_location=location_name
        )
